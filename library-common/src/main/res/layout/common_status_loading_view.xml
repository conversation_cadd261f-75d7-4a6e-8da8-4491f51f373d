<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:background="@color/common_color_FFFFFF"
    android:gravity="center_horizontal"
    android:orientation="vertical">

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="2" />

    <FrameLayout
        android:layout_marginBottom="30dp"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content">

        <ImageView
            android:id="@+id/image"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            tools:ignore="ContentDescription"
            tools:src="@drawable/common_data_none" />

        <ImageView
            android:visibility="gone"
            android:src="@drawable/common_loading_inside"
            android:layout_gravity="center"
            android:id="@+id/centerImage"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content" />
    </FrameLayout>

    <TextView
        android:id="@+id/errorBoldView"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:layout_marginBottom="7dp"
        android:gravity="center_horizontal"
        android:text="网络连接失败,请检查网络后"
        android:textColor="#878794"
        android:textSize="19dp"
        android:textStyle="bold"
        android:visibility="gone" />

    <TextView
        android:layout_marginTop="20dp"
        android:id="@+id/text"
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:gravity="center_horizontal"
        android:textColor="@color/common_color_999999"
        android:textSize="19dp"
        android:textStyle="bold"
        tools:text="点击重试" />

    <View
        android:layout_width="match_parent"
        android:layout_height="0dp"
        android:layout_weight="4" />
</LinearLayout>
