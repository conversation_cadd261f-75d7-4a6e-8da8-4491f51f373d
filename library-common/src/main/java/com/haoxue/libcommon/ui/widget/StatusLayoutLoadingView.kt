package com.haoxue.libcommon.ui.widget

import android.annotation.SuppressLint
import android.content.Context
import android.view.LayoutInflater
import android.view.View
import android.widget.ImageView
import android.widget.LinearLayout
import android.widget.TextView
import com.alguojian.mylibrary.StatusLayout
import com.haoxue.libcommon.R
import com.haoxue.libcommon.singleClick

/**
 * 默认的加载视图view
 */
@SuppressLint("ViewConstructor")
class StatusLayoutLoadingView(context: Context, private val mOnClickListener: (() -> Unit?)? = null) :
        LinearLayout(context) {

    private val mTextView: TextView
    private val mImageView: ImageView
    private val errorBoldView: TextView
    private val centerImage: ImageView

    init {
        LayoutInflater.from(context).inflate(R.layout.common_status_loading_view, this, true)
        mImageView = findViewById(R.id.image)
        mTextView = findViewById(R.id.text)
        errorBoldView = findViewById(R.id.errorBoldView)
        centerImage = findViewById(R.id.centerImage)
        setBackgroundColor(-0xf0f10)
    }

    /**
     * 设置是否显示图示文案
     */
    fun setTextVisibility(visible: Boolean) {
        mTextView.visibility = if (visible) View.VISIBLE else View.GONE
    }

    /**
     * 设置新状态的view信息
     */
    fun setStatus(status: Int) {
        var show = true
        var image = R.drawable.common_status_loading
        var str = ""
        errorBoldView.visibility = if (status == StatusLayout.STATUS_LAYOUT_STATUS_FAIL) View.VISIBLE else View.GONE
        centerImage.visibility = if (status == StatusLayout.STATUS_LAYOUT_STATUS_LOADING) View.VISIBLE else View.GONE
        mTextView.visibility = if (status == StatusLayout.STATUS_LAYOUT_STATUS_LOADING) View.GONE else View.VISIBLE

        when (status) {
            StatusLayout.STATUS_LAYOUT_STATUS_SUCCESS -> show = false
            StatusLayout.STATUS_LAYOUT_STATUS_LOADING -> {
                str = "加载中..."
            }
            StatusLayout.STATUS_LAYOUT_STATUS_FAIL -> {
                str = "点击重试"
                image = R.drawable.common_data_none
            }
            StatusLayout.STATUS_LAYOUT_STATUS_EMPTY -> {
                str = "暂无数据"
                image = R.drawable.common_content_none
            }
        }
        mImageView.setImageResource(image)

        singleClick {
            if (status == StatusLayout.STATUS_LAYOUT_STATUS_FAIL) {
                mOnClickListener?.invoke()
            }
        }

        mTextView.text = str
        visibility = if (show) View.VISIBLE else View.GONE

    }
}
