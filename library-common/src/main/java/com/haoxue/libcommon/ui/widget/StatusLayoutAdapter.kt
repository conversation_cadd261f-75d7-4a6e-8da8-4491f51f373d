package com.haoxue.libcommon.ui.widget

import android.view.View
import com.alguojian.mylibrary.StatusAdapter
import com.alguojian.mylibrary.StatusLayout.StatusHelper

class StatusLayoutAdapter : StatusAdapter {
    override fun getView(statusHelper: StatusHelper, statusView: View?, status: Int): View? {
        var defaultLoadingView: StatusLayoutLoadingView? = null
        if (statusView != null && statusView is StatusLayoutLoadingView) defaultLoadingView = statusView
        if (defaultLoadingView == null) {
            defaultLoadingView = StatusLayoutLoadingView(statusHelper.context!!, statusHelper.click)
        }
        defaultLoadingView.setStatus(status)
        return defaultLoadingView

    }
}